// Responsive functionality and interactions
document.addEventListener('DOMContentLoaded', function() {
    
    // Handle form submission
    const loginForm = document.querySelector('.login-form');
    const emailInput = document.getElementById('email');
    const passwordInput = document.getElementById('password');
    const loginBtn = document.querySelector('.login-btn');
    const googleBtn = document.querySelector('.google-btn');
    
    // Login form submission
    loginForm.addEventListener('submit', function(e) {
        e.preventDefault();
        
        const email = emailInput.value.trim();
        const password = passwordInput.value.trim();
        
        if (!email || !password) {
            alert('Please fill in all fields');
            return;
        }
        
        // Simulate login process
        loginBtn.textContent = 'Logging in...';
        loginBtn.disabled = true;
        
        setTimeout(() => {
            alert('Login functionality would be implemented here');
            loginBtn.textContent = 'Login Now';
            loginBtn.disabled = false;
        }, 1500);
    });
    
    // Google login
    googleBtn.addEventListener('click', function() {
        alert('Google OAuth integration would be implemented here');
    });
    
    // Handle viewport changes for better mobile experience
    function handleViewportChange() {
        const vh = window.innerHeight * 0.01;
        document.documentElement.style.setProperty('--vh', `${vh}px`);
    }
    
    // Set initial viewport height
    handleViewportChange();
    
    // Update on resize and orientation change
    window.addEventListener('resize', handleViewportChange);
    window.addEventListener('orientationchange', function() {
        setTimeout(handleViewportChange, 100);
    });
    
    // Smooth focus transitions for inputs
    const inputs = document.querySelectorAll('input');
    inputs.forEach(input => {
        input.addEventListener('focus', function() {
            this.parentElement.classList.add('focused');
        });
        
        input.addEventListener('blur', function() {
            this.parentElement.classList.remove('focused');
        });
    });
    
    // Add loading states for better UX
    function addLoadingState(button, originalText, loadingText) {
        button.addEventListener('click', function() {
            if (this.disabled) return;
            
            this.textContent = loadingText;
            this.disabled = true;
            
            setTimeout(() => {
                this.textContent = originalText;
                this.disabled = false;
            }, 2000);
        });
    }
    
    // Keyboard navigation improvements
    document.addEventListener('keydown', function(e) {
        // Enter key on email field focuses password
        if (e.key === 'Enter' && document.activeElement === emailInput) {
            e.preventDefault();
            passwordInput.focus();
        }
        
        // Enter key on password field submits form
        if (e.key === 'Enter' && document.activeElement === passwordInput) {
            e.preventDefault();
            loginForm.dispatchEvent(new Event('submit'));
        }
    });
    
    // Add ripple effect for buttons on touch devices
    function createRipple(event) {
        const button = event.currentTarget;
        const circle = document.createElement('span');
        const diameter = Math.max(button.clientWidth, button.clientHeight);
        const radius = diameter / 2;
        
        circle.style.width = circle.style.height = `${diameter}px`;
        circle.style.left = `${event.clientX - button.offsetLeft - radius}px`;
        circle.style.top = `${event.clientY - button.offsetTop - radius}px`;
        circle.classList.add('ripple');
        
        const ripple = button.getElementsByClassName('ripple')[0];
        if (ripple) {
            ripple.remove();
        }
        
        button.appendChild(circle);
    }
    
    // Apply ripple effect to buttons
    const buttons = document.querySelectorAll('.login-btn, .google-btn');
    buttons.forEach(button => {
        button.addEventListener('click', createRipple);
    });
    
    // Detect if user is on mobile device
    const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
    
    if (isMobile) {
        document.body.classList.add('mobile-device');
        
        // Prevent zoom on input focus for iOS
        const inputs = document.querySelectorAll('input');
        inputs.forEach(input => {
            input.addEventListener('focus', function() {
                if (window.innerWidth < 768) {
                    document.querySelector('meta[name=viewport]').setAttribute('content', 
                        'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0');
                }
            });
            
            input.addEventListener('blur', function() {
                if (window.innerWidth < 768) {
                    document.querySelector('meta[name=viewport]').setAttribute('content', 
                        'width=device-width, initial-scale=1.0');
                }
            });
        });
    }
    
    // Performance optimization: Debounce resize events
    let resizeTimeout;
    window.addEventListener('resize', function() {
        clearTimeout(resizeTimeout);
        resizeTimeout = setTimeout(function() {
            handleViewportChange();
        }, 100);
    });
    
    console.log('SaleSkip Login Page - Fully Responsive Version Loaded');
});
