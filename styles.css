* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    height: 100vh;
    overflow: hidden;
}

.container {
    display: flex;
    height: 100vh;
}

/* Left Section - Blue Side */
.left-section {
    flex: 1;
    background: linear-gradient(135deg, #4F46E5 0%, #7C3AED 50%, #3B82F6 100%);
    position: relative;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    padding: 60px 80px;
    overflow: hidden;
}

.background-shapes {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
}

.shape {
    position: absolute;
    border: 2px solid rgba(255, 255, 255, 0.1);
    border-radius: 20px;
}

.shape-1 {
    width: 300px;
    height: 200px;
    top: 10%;
    right: -50px;
    transform: rotate(15deg);
}

.shape-2 {
    width: 250px;
    height: 180px;
    top: 40%;
    right: -80px;
    transform: rotate(-10deg);
}

.shape-3 {
    width: 200px;
    height: 150px;
    bottom: 20%;
    right: -30px;
    transform: rotate(25deg);
}

.content {
    z-index: 2;
}

.icon {
    margin-bottom: 40px;
}

.main-title {
    font-size: 64px;
    font-weight: 700;
    color: white;
    line-height: 1.1;
    margin-bottom: 30px;
}

.wave {
    font-size: 56px;
    margin-left: 10px;
}

.subtitle {
    font-size: 18px;
    color: rgba(255, 255, 255, 0.9);
    line-height: 1.6;
    font-weight: 400;
}

.footer {
    z-index: 2;
}

.footer p {
    color: rgba(255, 255, 255, 0.7);
    font-size: 14px;
    font-weight: 400;
}

/* Right Section - Login Form */
.right-section {
    flex: 1;
    background: #FAFAFA;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 40px;
}

.login-container {
    width: 100%;
    max-width: 400px;
}

.logo h2 {
    font-size: 32px;
    font-weight: 700;
    color: #1F2937;
    margin-bottom: 60px;
}

.welcome-section {
    margin-bottom: 40px;
}

.welcome-section h3 {
    font-size: 28px;
    font-weight: 600;
    color: #1F2937;
    margin-bottom: 12px;
}

.account-text {
    font-size: 14px;
    color: #6B7280;
    line-height: 1.5;
}

.create-account {
    color: #4F46E5;
    text-decoration: underline;
    font-weight: 500;
}

.create-account:hover {
    color: #3730A3;
}

.login-form {
    width: 100%;
}

.input-group {
    margin-bottom: 20px;
}

.input-group input {
    width: 100%;
    padding: 16px 20px;
    border: 1px solid #D1D5DB;
    border-radius: 8px;
    font-size: 16px;
    background: white;
    transition: border-color 0.2s;
}

.input-group input:focus {
    outline: none;
    border-color: #4F46E5;
    box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

.input-group input[readonly] {
    background: white;
    color: #374151;
}

.login-btn {
    width: 100%;
    padding: 16px;
    background: #1F2937;
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    margin-bottom: 16px;
    transition: background-color 0.2s;
}

.login-btn:hover {
    background: #111827;
}

.login-btn:active {
    transform: translateY(1px);
}

.google-btn {
    width: 100%;
    padding: 16px;
    background: white;
    color: #374151;
    border: 1px solid #D1D5DB;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
    margin-bottom: 24px;
    transition: all 0.2s;
    -webkit-tap-highlight-color: transparent;
}

.google-btn:hover {
    background: #F9FAFB;
    border-color: #9CA3AF;
}

.google-btn:active {
    transform: translateY(1px);
}

/* Interactive Elements */
.input-group.focused input {
    border-color: #4F46E5;
    box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

.login-btn,
.google-btn {
    position: relative;
    overflow: hidden;
}

.ripple {
    position: absolute;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.3);
    transform: scale(0);
    animation: ripple-animation 0.6s linear;
    pointer-events: none;
}

@keyframes ripple-animation {
    to {
        transform: scale(4);
        opacity: 0;
    }
}

/* Custom viewport height for mobile */
.container {
    height: 100vh;
    height: calc(var(--vh, 1vh) * 100);
}

/* Touch-friendly improvements */
@media (hover: none) and (pointer: coarse) {

    .login-btn,
    .google-btn {
        min-height: 48px;
        padding: 16px;
    }

    .input-group input {
        min-height: 48px;
        padding: 16px 20px;
    }

    .create-account,
    .click-here {
        min-height: 44px;
        display: inline-block;
        line-height: 44px;
        padding: 0 8px;
    }

    /* Larger tap targets for mobile */
    .forgot-password {
        padding: 8px 0;
    }
}

/* High DPI displays */
@media (-webkit-min-device-pixel-ratio: 2),
(min-resolution: 192dpi) {
    .icon svg {
        image-rendering: -webkit-optimize-contrast;
        image-rendering: crisp-edges;
    }
}

.forgot-password {
    text-align: center;
    font-size: 14px;
    color: #6B7280;
}

.click-here {
    color: #1F2937;
    text-decoration: underline;
    font-weight: 500;
    margin-left: 4px;
}

.click-here:hover {
    color: #111827;
}

/* Responsive Design - Complete Breakpoints */

/* Extra Large Screens (1440px and up) */
@media (min-width: 1440px) {
    .left-section {
        padding: 80px 120px;
    }

    .main-title {
        font-size: 72px;
    }

    .subtitle {
        font-size: 20px;
    }

    .login-container {
        max-width: 450px;
    }
}

/* Large Screens (1200px to 1439px) */
@media (min-width: 1200px) and (max-width: 1439px) {
    .left-section {
        padding: 70px 100px;
    }

    .main-title {
        font-size: 68px;
    }

    .subtitle {
        font-size: 19px;
    }
}

/* Desktop (992px to 1199px) */
@media (min-width: 992px) and (max-width: 1199px) {
    .left-section {
        padding: 60px 70px;
    }

    .main-title {
        font-size: 58px;
    }

    .subtitle {
        font-size: 17px;
    }

    .login-container {
        max-width: 380px;
    }
}

/* Tablet Landscape (768px to 991px) */
@media (min-width: 768px) and (max-width: 991px) {
    .left-section {
        padding: 50px 60px;
    }

    .main-title {
        font-size: 52px;
    }

    .subtitle {
        font-size: 16px;
    }

    .login-container {
        max-width: 360px;
    }

    .shape-1 {
        width: 250px;
        height: 170px;
    }

    .shape-2 {
        width: 200px;
        height: 150px;
    }

    .shape-3 {
        width: 150px;
        height: 120px;
    }
}

/* Tablet Portrait (576px to 767px) */
@media (min-width: 576px) and (max-width: 767px) {
    .container {
        flex-direction: column;
    }

    .left-section {
        padding: 40px 50px;
        min-height: 45vh;
        justify-content: center;
    }

    .main-title {
        font-size: 48px;
        margin-bottom: 20px;
    }

    .subtitle {
        font-size: 16px;
        margin-bottom: 30px;
    }

    .icon {
        margin-bottom: 30px;
    }

    .icon svg {
        width: 50px;
        height: 50px;
    }

    .footer {
        position: absolute;
        bottom: 20px;
        left: 50px;
        right: 50px;
    }

    .right-section {
        padding: 40px 50px;
        min-height: 55vh;
    }

    .login-container {
        max-width: 100%;
    }

    .logo h2 {
        font-size: 28px;
        margin-bottom: 40px;
    }

    .welcome-section h3 {
        font-size: 24px;
    }

    .shape-1,
    .shape-2,
    .shape-3 {
        display: none;
    }
}

/* Mobile Large (480px to 575px) */
@media (min-width: 480px) and (max-width: 575px) {
    .container {
        flex-direction: column;
    }

    .left-section {
        padding: 30px 40px;
        min-height: 40vh;
        justify-content: center;
    }

    .main-title {
        font-size: 42px;
        margin-bottom: 15px;
    }

    .wave {
        font-size: 38px;
    }

    .subtitle {
        font-size: 15px;
        margin-bottom: 25px;
    }

    .icon {
        margin-bottom: 25px;
    }

    .icon svg {
        width: 45px;
        height: 45px;
    }

    .footer {
        position: absolute;
        bottom: 15px;
        left: 40px;
        right: 40px;
    }

    .footer p {
        font-size: 12px;
    }

    .right-section {
        padding: 30px 40px;
        min-height: 60vh;
    }

    .logo h2 {
        font-size: 26px;
        margin-bottom: 35px;
    }

    .welcome-section h3 {
        font-size: 22px;
    }

    .account-text {
        font-size: 13px;
    }

    .input-group input {
        padding: 14px 18px;
        font-size: 15px;
    }

    .login-btn,
    .google-btn {
        padding: 14px;
        font-size: 15px;
    }

    .shape-1,
    .shape-2,
    .shape-3 {
        display: none;
    }
}

/* Mobile Medium (375px to 479px) */
@media (min-width: 375px) and (max-width: 479px) {
    .container {
        flex-direction: column;
    }

    .left-section {
        padding: 25px 30px;
        min-height: 38vh;
        justify-content: center;
    }

    .main-title {
        font-size: 36px;
        margin-bottom: 12px;
    }

    .wave {
        font-size: 32px;
    }

    .subtitle {
        font-size: 14px;
        margin-bottom: 20px;
    }

    .icon {
        margin-bottom: 20px;
    }

    .icon svg {
        width: 40px;
        height: 40px;
    }

    .footer {
        position: absolute;
        bottom: 12px;
        left: 30px;
        right: 30px;
    }

    .footer p {
        font-size: 11px;
    }

    .right-section {
        padding: 25px 30px;
        min-height: 62vh;
    }

    .logo h2 {
        font-size: 24px;
        margin-bottom: 30px;
    }

    .welcome-section {
        margin-bottom: 30px;
    }

    .welcome-section h3 {
        font-size: 20px;
        margin-bottom: 10px;
    }

    .account-text {
        font-size: 12px;
    }

    .input-group {
        margin-bottom: 16px;
    }

    .input-group input {
        padding: 12px 16px;
        font-size: 14px;
    }

    .login-btn,
    .google-btn {
        padding: 12px;
        font-size: 14px;
    }

    .google-btn {
        margin-bottom: 20px;
    }

    .forgot-password {
        font-size: 12px;
    }

    .shape-1,
    .shape-2,
    .shape-3 {
        display: none;
    }
}

/* Mobile Small (320px to 374px) */
@media (max-width: 374px) {
    .container {
        flex-direction: column;
    }

    .left-section {
        padding: 20px 25px;
        min-height: 35vh;
        justify-content: center;
    }

    .main-title {
        font-size: 32px;
        margin-bottom: 10px;
    }

    .wave {
        font-size: 28px;
    }

    .subtitle {
        font-size: 13px;
        margin-bottom: 15px;
    }

    .icon {
        margin-bottom: 15px;
    }

    .icon svg {
        width: 35px;
        height: 35px;
    }

    .footer {
        position: absolute;
        bottom: 10px;
        left: 25px;
        right: 25px;
    }

    .footer p {
        font-size: 10px;
    }

    .right-section {
        padding: 20px 25px;
        min-height: 65vh;
    }

    .logo h2 {
        font-size: 22px;
        margin-bottom: 25px;
    }

    .welcome-section {
        margin-bottom: 25px;
    }

    .welcome-section h3 {
        font-size: 18px;
        margin-bottom: 8px;
    }

    .account-text {
        font-size: 11px;
    }

    .input-group {
        margin-bottom: 14px;
    }

    .input-group input {
        padding: 10px 14px;
        font-size: 13px;
    }

    .login-btn,
    .google-btn {
        padding: 10px;
        font-size: 13px;
    }

    .google-btn {
        margin-bottom: 18px;
    }

    .google-btn svg {
        width: 16px;
        height: 16px;
    }

    .forgot-password {
        font-size: 11px;
    }

    .shape-1,
    .shape-2,
    .shape-3 {
        display: none;
    }
}

/* Landscape Orientation for Mobile */
@media (max-height: 500px) and (orientation: landscape) {
    .container {
        flex-direction: row;
    }

    .left-section {
        min-height: 100vh;
        padding: 20px 30px;
    }

    .main-title {
        font-size: 28px;
        margin-bottom: 8px;
    }

    .wave {
        font-size: 24px;
    }

    .subtitle {
        font-size: 12px;
        margin-bottom: 10px;
    }

    .icon {
        margin-bottom: 10px;
    }

    .icon svg {
        width: 30px;
        height: 30px;
    }

    .footer {
        position: absolute;
        bottom: 10px;
        left: 30px;
        right: 30px;
    }

    .footer p {
        font-size: 9px;
    }

    .right-section {
        padding: 20px 30px;
    }

    .logo h2 {
        font-size: 20px;
        margin-bottom: 15px;
    }

    .welcome-section {
        margin-bottom: 15px;
    }

    .welcome-section h3 {
        font-size: 16px;
        margin-bottom: 5px;
    }

    .account-text {
        font-size: 10px;
    }

    .input-group {
        margin-bottom: 10px;
    }

    .input-group input {
        padding: 8px 12px;
        font-size: 12px;
    }

    .login-btn,
    .google-btn {
        padding: 8px;
        font-size: 12px;
    }

    .google-btn {
        margin-bottom: 10px;
    }

    .forgot-password {
        font-size: 10px;
    }

    .shape-1,
    .shape-2,
    .shape-3 {
        display: none;
    }
}